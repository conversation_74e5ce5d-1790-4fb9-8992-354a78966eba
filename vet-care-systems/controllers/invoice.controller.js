import Invoice from "../models/invoice.model.js";
import Appointment from "../models/appointment.model.js";
import HealthRecord from "../models/healthRecord.model.js";
import Discount from "../models/discount.model.js";
import { sendResponse } from '../utils/responseHandler.js';

/**
 * Generate invoice from appointment
 */
export const generateInvoiceFromAppointment = async (req, res) => {
    try {
        const { appointmentId } = req.params;

        // Get appointment details with pet data to get clientId
        const appointment = await Appointment.findOne({
            appointmentId: parseInt(appointmentId)
        }).lean();

        if (!appointment) {
            return sendResponse(res, 404, false, "Appointment not found");
        }

        // Get pet data to find the client ID if not directly available
        let clientId = appointment.clientId;

        if (!clientId && appointment.petId) {
            const Pet = (await import("../models/pet.model.js")).default;
            const pet = await Pet.findOne({ petId: appointment.petId }).lean();
            if (pet) {
                clientId = pet.clientId || pet.owner;
            }
        }

        // Validate required fields for invoice generation
        if (!clientId) {
            return sendResponse(res, 400, false, "Cannot determine client ID for this appointment");
        }

        if (!appointment.petId) {
            return sendResponse(res, 400, false, "Appointment must have a pet ID to generate invoice");
        }

        // Check if invoice already exists
        const existingInvoice = await Invoice.findOne({ appointmentId: appointment.appointmentId });
        if (existingInvoice) {
            return sendResponse(res, 200, true, "Invoice already exists", existingInvoice);
        }

        // Get health record if appointment is completed
        let healthRecord = null;
        if (appointment.status === 'completed') {
            healthRecord = await HealthRecord.findOne({
                appointmentId: appointment.appointmentId
            }).lean();
        }

        // Calculate service charges
        const services = [];
        const appointmentType = appointment.appointmentTypes?.[0];
        if (appointmentType) {
            services.push({
                serviceId: appointmentType.appointmentTypeId,
                serviceName: appointmentType.name || 'Veterinary Service',
                description: appointment.reason,
                quantity: 1,
                unitPrice: appointmentType.price || 0,
                totalPrice: appointmentType.price || 0
            });
        }

        // Add medications from health record
        const medications = [];
        if (healthRecord?.medications?.length > 0) {
            healthRecord.medications.forEach((med, index) => {
                medications.push({
                    inventoryItemId: index + 1, // Placeholder
                    medicationName: med.name,
                    quantity: 1,
                    unitPrice: 100, // Default price - should come from inventory
                    totalPrice: 100
                });
            });
        }

        // Calculate subtotal
        const servicesTotal = services.reduce((sum, service) => sum + service.totalPrice, 0);
        const medicationsTotal = medications.reduce((sum, med) => sum + med.totalPrice, 0);
        const subtotal = servicesTotal + medicationsTotal;

        // Add after-hours charge if applicable
        const afterHoursCharge = healthRecord?.billingDetails?.afterHoursCharge || 0;

        // Calculate tax (16% VAT for Kenya)
        const taxRate = 16;
        const taxableAmount = subtotal + afterHoursCharge;
        const taxAmount = (taxableAmount * taxRate) / 100;

        // Calculate total
        const totalAmount = taxableAmount + taxAmount;

        // Set due date (7 days from invoice date)
        const dueDate = new Date();
        dueDate.setDate(dueDate.getDate() + 7);

        // Generate invoice number
        const invoiceCount = await Invoice.countDocuments();
        const invoiceNumber = `INV-${String(invoiceCount + 1).padStart(6, '0')}`;

        // Create invoice
        const invoice = await Invoice.create({
            invoiceNumber,
            appointmentId: appointment.appointmentId,
            healthRecordId: healthRecord?.healthRecordId,
            clientId: clientId, // Use the resolved clientId
            petId: appointment.petId,
            clinicId: appointment.clinicId || req.user?.clinicId || 1019,
            dueDate,
            services,
            medications,
            subtotal,
            afterHoursCharge,
            taxRate,
            taxAmount,
            totalAmount,
            amountDue: totalAmount,
            currency: appointmentType?.currency || 'KES',
            generatedBy: req.user?.staffId || req.user?.userId || 1001
        });

        return sendResponse(res, 201, true, "Invoice generated successfully", invoice);
    } catch (error) {
        console.error("Generate invoice error:", error);
        return sendResponse(res, 500, false, `Failed to generate invoice: ${error.message}`);
    }
};

/**
 * Get invoice by ID
 */
export const getInvoiceById = async (req, res) => {
    try {
        const { invoiceId } = req.params;

        const invoice = await Invoice.findOne({
            invoiceId: parseInt(invoiceId)
        }).lean();

        if (!invoice) {
            return sendResponse(res, 404, false, "Invoice not found");
        }

        // Get related data
        const [appointment, healthRecord] = await Promise.all([
            Appointment.findOne({ appointmentId: invoice.appointmentId }).lean(),
            invoice.healthRecordId ? HealthRecord.findOne({ healthRecordId: invoice.healthRecordId }).lean() : null
        ]);

        // Add related data to invoice
        invoice.appointmentData = appointment;
        invoice.healthRecordData = healthRecord;

        return sendResponse(res, 200, true, "Invoice retrieved successfully", invoice);
    } catch (error) {
        console.error("Get invoice error:", error);
        return sendResponse(res, 500, false, `Failed to retrieve invoice: ${error.message}`);
    }
};

/**
 * Get invoice by appointment ID
 */
export const getInvoiceByAppointment = async (req, res) => {
    try {
        const { appointmentId } = req.params;

        const invoice = await Invoice.findOne({
            appointmentId: parseInt(appointmentId)
        }).lean();

        if (!invoice) {
            return sendResponse(res, 404, false, "Invoice not found for this appointment");
        }

        return sendResponse(res, 200, true, "Invoice retrieved successfully", invoice);
    } catch (error) {
        console.error("Get invoice by appointment error:", error);
        return sendResponse(res, 500, false, `Failed to retrieve invoice: ${error.message}`);
    }
};

/**
 * Apply discount to invoice
 */
export const applyDiscountToInvoice = async (req, res) => {
    try {
        const { invoiceId } = req.params;
        const { discountType, discountValue, reason } = req.body;

        const invoice = await Invoice.findOne({ invoiceId: parseInt(invoiceId) });

        if (!invoice) {
            return sendResponse(res, 404, false, "Invoice not found");
        }

        if (invoice.paymentStatus === 'paid') {
            return sendResponse(res, 400, false, "Cannot apply discount to paid invoice");
        }

        // Calculate discount amount
        let discountAmount = 0;
        if (discountType === 'percentage') {
            discountAmount = (invoice.subtotal * discountValue) / 100;
        } else {
            discountAmount = discountValue;
        }

        // Create discount record
        const discount = await Discount.create({
            healthRecordId: invoice.healthRecordId,
            appointmentId: invoice.appointmentId,
            discountType,
            discountValue,
            discountAmount,
            reason,
            authorizedBy: req.user?.staffId || req.user?.userId || 1001,
            clientId: invoice.clientId,
            clinicId: invoice.clinicId
        });

        // Add discount to invoice
        invoice.discounts.push({
            discountId: discount.discountId,
            discountType,
            discountValue,
            discountAmount,
            reason,
            appliedBy: req.user?.staffId || req.user?.userId || 1001
        });

        // Recalculate totals
        invoice.totalDiscounts = invoice.discounts.reduce((sum, d) => sum + d.discountAmount, 0);
        const taxableAmount = invoice.subtotal + invoice.afterHoursCharge - invoice.totalDiscounts;
        invoice.taxAmount = (taxableAmount * invoice.taxRate) / 100;
        invoice.totalAmount = taxableAmount + invoice.taxAmount;
        invoice.amountDue = invoice.totalAmount - invoice.amountPaid;

        await invoice.save();

        return sendResponse(res, 200, true, "Discount applied successfully", invoice);
    } catch (error) {
        console.error("Apply discount error:", error);
        return sendResponse(res, 500, false, `Failed to apply discount: ${error.message}`);
    }
};

/**
 * Update invoice status
 */
export const updateInvoiceStatus = async (req, res) => {
    try {
        const { invoiceId } = req.params;
        const { status } = req.body;

        const invoice = await Invoice.findOneAndUpdate(
            { invoiceId: parseInt(invoiceId) },
            { status },
            { new: true }
        );

        if (!invoice) {
            return sendResponse(res, 404, false, "Invoice not found");
        }

        return sendResponse(res, 200, true, "Invoice status updated successfully", invoice);
    } catch (error) {
        console.error("Update invoice status error:", error);
        return sendResponse(res, 500, false, `Failed to update invoice status: ${error.message}`);
    }
};
