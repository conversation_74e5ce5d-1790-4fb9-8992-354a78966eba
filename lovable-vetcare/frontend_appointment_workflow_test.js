/**
 * Frontend Appointment Workflow Test
 * Tests the complete appointment workflow from frontend perspective
 * Simulates API calls that the frontend would make
 */

const API_BASE_URL = 'http://localhost:5500/api/v1';

// Test configuration
const TEST_CONFIG = {
  clinicId: 1001, // Adolf Clinic
  staffId: 1001,  // Test staff
  token: null     // Will be set after login
};

// Test data for appointments
const TEST_APPOINTMENTS = [
  {
    clientName: '<PERSON>',
    petName: 'Buddy',
    species: 'Dog',
    breed: 'German Shepherd',
    appointmentTypes: ['Vaccination', 'Consultation']
  },
  {
    clientName: '<PERSON>',
    petName: '<PERSON>',
    species: 'Cat',
    breed: 'Puss cat',
    appointmentTypes: ['Laboratory', 'Surgery']
  },
  {
    clientName: '<PERSON>',
    petName: 'Max',
    species: 'Dog',
    breed: 'German Shepherd',
    appointmentTypes: ['Emergency', 'Medication']
  },
  {
    clientName: '<PERSON>',
    petName: '<PERSON>',
    species: 'Cat',
    breed: 'Puss cat',
    appointmentTypes: ['Grooming', 'Dental']
  }
];

class FrontendWorkflowTester {
  constructor() {
    this.appointments = [];
    this.invoices = [];
    this.receipts = [];
  }

  // Utility method to make API calls
  async apiCall(endpoint, method = 'GET', data = null) {
    const url = `${API_BASE_URL}${endpoint}`;
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...(TEST_CONFIG.token && { 'Authorization': `Bearer ${TEST_CONFIG.token}` })
      }
    };

    if (data && (method === 'POST' || method === 'PUT')) {
      options.body = JSON.stringify(data);
    }

    try {
      const response = await fetch(url, options);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(`API Error: ${result.message || response.statusText}`);
      }

      return result;
    } catch (error) {
      console.error(`❌ API call failed: ${method} ${endpoint}`, error.message);
      throw error;
    }
  }

  // Step 1: Login to get authentication token
  async login() {
    console.log('🔐 Logging in...');

    try {
      // Try with the default admin credentials first
      let response = await this.apiCall('/auth/sign-in', 'POST', {
        email: '<EMAIL>',
        password: 'pass123'
      });

      if (response.success && response.data?.token) {
        TEST_CONFIG.token = response.data.token;
        console.log('✅ Login successful with admin credentials');
        return true;
      }

      // If admin login fails, try with a staff account
      response = await this.apiCall('/auth/sign-in', 'POST', {
        email: '<EMAIL>',
        password: 'password123'
      });

      if (response.success && response.data?.token) {
        TEST_CONFIG.token = response.data.token;
        console.log('✅ Login successful with staff credentials');
        return true;
      }

      throw new Error('Login failed - no token received');
    } catch (error) {
      console.error('❌ Login failed:', error.message);
      return false;
    }
  }

  // Step 2: Fetch existing appointments
  async fetchAppointments() {
    console.log('\n📋 Fetching appointments...');

    try {
      const response = await this.apiCall('/appointments?limit=10&status=scheduled');

      if (response.success && response.data?.data) {
        this.appointments = response.data.data.slice(0, 4); // Take first 4 appointments
        console.log(`✅ Found ${this.appointments.length} appointments`);

        // Display appointment details
        this.appointments.forEach((apt, index) => {
          console.log(`   ${index + 1}. Appointment ${apt.appointmentId}: ${apt.petName} (${apt.clientName})`);
        });

        return true;
      } else {
        throw new Error('No appointments found');
      }
    } catch (error) {
      console.error('❌ Failed to fetch appointments:', error.message);
      return false;
    }
  }

  // Step 3: Start appointment workflow for each appointment
  async processAppointmentWorkflow(appointment) {
    console.log(`\n🔄 Processing workflow for Appointment ${appointment.appointmentId}`);
    console.log(`   Pet: ${appointment.petName} | Client: ${appointment.clientName}`);

    try {
      // Step 3a: Update appointment status to in_progress
      await this.updateAppointmentStatus(appointment.appointmentId, 'in_progress');

      // Step 3b: Assign categories to staff
      await this.assignCategories(appointment);

      // Step 3c: Add services to appointment
      await this.addServices(appointment);

      // Step 3d: Generate AI suggestions
      await this.generateAISuggestions(appointment);

      // Step 3e: Complete appointment
      await this.completeAppointment(appointment);

      // Step 3f: Generate invoice
      const invoice = await this.generateInvoice(appointment);

      // Step 3g: Process payment
      const payment = await this.processPayment(invoice);

      // Step 3h: Generate receipt
      const receipt = await this.generateReceipt(appointment);

      console.log(`✅ Completed workflow for Appointment ${appointment.appointmentId}`);
      return { invoice, payment, receipt };

    } catch (error) {
      console.error(`❌ Workflow failed for Appointment ${appointment.appointmentId}:`, error.message);
      throw error;
    }
  }

  // Update appointment status
  async updateAppointmentStatus(appointmentId, status) {
    console.log(`  📝 Updating appointment status to: ${status}`);

    const response = await this.apiCall(`/appointments/${appointmentId}`, 'PUT', {
      status: status
    });

    if (response.success) {
      console.log(`    ✅ Status updated to: ${status}`);
    }
    return response;
  }

  // Assign categories to staff
  async assignCategories(appointment) {
    console.log(`  👥 Assigning categories to staff...`);

    // Map appointment types to categories
    const categoryMapping = {
      'Vaccination': 'vaccination',
      'Consultation': 'consultation',
      'Laboratory': 'laboratory',
      'Surgery': 'surgery',
      'Emergency': 'emergency',
      'Medication': 'medication',
      'Grooming': 'grooming',
      'Dental': 'dental'
    };

    // Get appointment types from test data
    const testData = TEST_APPOINTMENTS.find(t => t.petName === appointment.petName);
    const appointmentTypes = testData?.appointmentTypes || ['Consultation'];

    const selectedCategories = appointmentTypes.map(type => ({
      category: categoryMapping[type] || 'consultation',
      assignedStaff: TEST_CONFIG.staffId,
      estimatedDuration: 30,
      priority: type === 'Emergency' ? 'urgent' : 'normal',
      isCompleted: false
    }));

    const response = await this.apiCall(`/appointments/${appointment.appointmentId}`, 'PUT', {
      selectedCategories: selectedCategories
    });

    if (response.success) {
      console.log(`    ✅ Assigned ${selectedCategories.length} categories`);
    }
    return response;
  }

  // Add services to appointment
  async addServices(appointment) {
    console.log(`  🛠️  Adding services...`);

    // Default services based on categories
    const defaultServices = {
      'vaccination': { name: 'Vaccination Service', price: 500 },
      'consultation': { name: 'General Consultation', price: 300 },
      'laboratory': { name: 'Laboratory Tests', price: 800 },
      'surgery': { name: 'Surgical Procedure', price: 2000 },
      'emergency': { name: 'Emergency Treatment', price: 1500 },
      'medication': { name: 'Medication Administration', price: 200 },
      'grooming': { name: 'Pet Grooming', price: 600 },
      'dental': { name: 'Dental Care', price: 1000 }
    };

    // Get appointment types from test data
    const testData = TEST_APPOINTMENTS.find(t => t.petName === appointment.petName);
    const appointmentTypes = testData?.appointmentTypes || ['Consultation'];

    const services = [];
    for (const type of appointmentTypes) {
      const category = type.toLowerCase();
      const serviceData = defaultServices[category] || defaultServices['consultation'];

      // Create service via API
      const serviceResponse = await this.apiCall('/services', 'POST', {
        serviceName: serviceData.name,
        description: `${serviceData.name} for ${appointment.petName}`,
        defaultPrice: serviceData.price,
        estimatedDuration: 30,
        category: category === 'vaccination' ? 'vaccination' : 'consultation',
        currency: 'KES',
        appointmentId: appointment.appointmentId,
        isActive: true,
        isCustom: true
      });

      if (serviceResponse.success) {
        services.push({
          serviceId: serviceResponse.data.serviceId,
          serviceName: serviceData.name,
          price: serviceData.price,
          currency: 'KES',
          assignedStaffId: TEST_CONFIG.staffId
        });
      }
    }

    // Update appointment with services
    const response = await this.apiCall(`/appointments/${appointment.appointmentId}`, 'PUT', {
      services: services
    });

    if (response.success) {
      console.log(`    ✅ Added ${services.length} services`);
    }
    return response;
  }

  // Generate AI suggestions
  async generateAISuggestions(appointment) {
    console.log(`  🤖 Generating AI suggestions...`);

    try {
      const response = await this.apiCall(`/appointments/${appointment.appointmentId}/ai-suggestions`, 'POST', {
        symptoms: ['routine_checkup'],
        vitalSigns: {
          temperature: 38.5,
          heartRate: 120,
          weight: 25
        },
        previousConditions: [],
        currentMedications: [],
        allergies: [],
        requestedCategories: ['diagnosis', 'treatment'],
        clinicId: TEST_CONFIG.clinicId
      });

      if (response.success) {
        console.log(`    ✅ Generated AI suggestions`);
      }
      return response;
    } catch (error) {
      console.log(`    ⚠️  AI suggestions skipped: ${error.message}`);
      return { success: true }; // Continue workflow even if AI fails
    }
  }

  // Complete appointment
  async completeAppointment(appointment) {
    console.log(`  ✅ Completing appointment...`);

    const response = await this.apiCall(`/appointments/${appointment.appointmentId}`, 'PUT', {
      status: 'completed',
      notes: `Appointment completed successfully for ${appointment.petName}`
    });

    if (response.success) {
      console.log(`    ✅ Appointment marked as completed`);
    }
    return response;
  }

  // Generate invoice
  async generateInvoice(appointment) {
    console.log(`  💰 Generating invoice...`);

    try {
      const response = await this.apiCall('/invoices', 'POST', {
        appointmentId: appointment.appointmentId,
        clientId: appointment.clientId,
        petId: appointment.petId,
        clinicId: TEST_CONFIG.clinicId,
        dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
        currency: 'KES',
        generatedBy: TEST_CONFIG.staffId
      });

      if (response.success) {
        const invoice = response.data;
        this.invoices.push(invoice);
        console.log(`    ✅ Generated invoice ${invoice.invoiceNumber} - KES ${invoice.totalAmount}`);
        return invoice;
      }
    } catch (error) {
      console.error(`    ❌ Invoice generation failed: ${error.message}`);
      throw error;
    }
  }

  // Process payment
  async processPayment(invoice) {
    console.log(`  💳 Processing payment...`);

    try {
      const response = await this.apiCall(`/payments/invoice/${invoice.invoiceId}`, 'POST', {
        amount: invoice.totalAmount,
        paymentMethod: 'cash',
        description: `Payment for appointment ${invoice.appointmentId}`,
        paymentDetails: {
          receivedBy: TEST_CONFIG.staffId,
          notes: 'Test payment via frontend workflow'
        }
      });

      if (response.success) {
        const payment = response.data.payment;
        console.log(`    ✅ Processed payment of KES ${payment.amount} via ${payment.paymentMethod}`);
        return payment;
      }
    } catch (error) {
      console.error(`    ❌ Payment processing failed: ${error.message}`);
      throw error;
    }
  }

  // Generate receipt
  async generateReceipt(appointment) {
    console.log(`  🧾 Generating receipt...`);

    try {
      const response = await this.apiCall(`/receipts/appointment/${appointment.appointmentId}`);

      if (response.success) {
        const receipt = response.data;
        this.receipts.push(receipt);
        console.log(`    ✅ Generated receipt ${receipt.receiptNumber} - KES ${receipt.totalAmount}`);
        return receipt;
      }
    } catch (error) {
      console.error(`    ❌ Receipt generation failed: ${error.message}`);
      throw error;
    }
  }

  // Main test runner
  async runTest() {
    console.log('🚀 Starting Frontend Appointment Workflow Test');
    console.log('================================================\n');

    try {
      // Step 1: Login
      const loginSuccess = await this.login();
      if (!loginSuccess) {
        throw new Error('Login failed - cannot proceed with test');
      }

      // Step 2: Fetch appointments
      const appointmentsFound = await this.fetchAppointments();
      if (!appointmentsFound || this.appointments.length === 0) {
        throw new Error('No appointments found - cannot proceed with test');
      }

      // Step 3: Process each appointment through the workflow
      console.log(`\n🔄 Processing ${this.appointments.length} appointments through workflow...\n`);

      const results = [];
      for (let i = 0; i < this.appointments.length; i++) {
        const appointment = this.appointments[i];
        try {
          const result = await this.processAppointmentWorkflow(appointment);
          results.push({ appointment, ...result });
        } catch (error) {
          console.error(`❌ Failed to process appointment ${appointment.appointmentId}: ${error.message}`);
          results.push({ appointment, error: error.message });
        }
      }

      // Step 4: Display final results
      await this.displayResults(results);

      console.log('\n🎉 FRONTEND WORKFLOW TEST COMPLETED SUCCESSFULLY!');
      console.log('All appointments have been processed through the complete workflow.\n');

    } catch (error) {
      console.error('❌ Test failed:', error.message);
    }
  }

  // Display final results
  async displayResults(results) {
    console.log('\n📊 FINAL WORKFLOW RESULTS');
    console.log('==========================\n');

    for (let i = 0; i < results.length; i++) {
      const result = results[i];
      const appointment = result.appointment;

      console.log(`🏥 Appointment ${appointment.appointmentId}:`);
      console.log(`   Client: ${appointment.clientName}`);
      console.log(`   Pet: ${appointment.petName} (${appointment.petSpecies || 'Unknown'})`);
      console.log(`   Status: ${appointment.status}`);

      if (result.error) {
        console.log(`   ❌ Error: ${result.error}`);
      } else {
        if (result.invoice) {
          console.log(`   💰 Invoice: ${result.invoice.invoiceNumber} - KES ${result.invoice.totalAmount} (${result.invoice.paymentStatus || 'pending'})`);
        }
        if (result.receipt) {
          console.log(`   🧾 Receipt: ${result.receipt.receiptNumber} - KES ${result.receipt.totalAmount}`);
        }
      }
      console.log('');
    }

    // Summary statistics
    const successful = results.filter(r => !r.error).length;
    const failed = results.filter(r => r.error).length;
    const totalInvoiceAmount = this.invoices.reduce((sum, inv) => sum + (inv.totalAmount || 0), 0);

    console.log('📈 SUMMARY STATISTICS:');
    console.log(`   ✅ Successful workflows: ${successful}`);
    console.log(`   ❌ Failed workflows: ${failed}`);
    console.log(`   💰 Total invoice amount: KES ${totalInvoiceAmount}`);
    console.log(`   🧾 Receipts generated: ${this.receipts.length}`);
  }
}

// Run the test
const tester = new FrontendWorkflowTester();
tester.runTest().catch(console.error);
